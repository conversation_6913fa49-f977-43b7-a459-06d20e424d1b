@use '../../../scss/variables' as *;
@use '../../../scss/mixins' as *;

.container {
  padding: $spacing-6;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  @include flex-between;
  margin-bottom: $spacing-6;
  gap: $spacing-4;

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
  }
}

.titleSection {
  .title {
    font-size: $font-size-2xl;
    font-weight: $font-weight-bold;
    color: $gray-900;
    margin: 0 0 $spacing-2 0;
  }

  .subtitle {
    font-size: $font-size-base;
    color: $gray-600;
    margin: 0;
  }
}

.actions {
  @include flex-center;
  gap: $spacing-3;
}

.createButton {
  @include flex-center;
  gap: $spacing-2;
  white-space: nowrap;
}

.associationsCard {
  background: $white;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
}

.cardHeader {
  @include flex-between;
  align-items: center;
  gap: $spacing-4;

  h2 {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $gray-900;
    margin: 0;
  }

  @include mobile-only {
    flex-direction: column;
    align-items: flex-start;
  }
}

.searchContainer {
  @include flex-center;
  gap: $spacing-3;
}

.searchBox {
  position: relative;
  min-width: 300px;

  @include mobile-only {
    min-width: 100%;
  }
}

.searchIcon {
  position: absolute;
  left: $spacing-3;
  top: 50%;
  transform: translateY(-50%);
  color: $gray-400;
  font-size: $font-size-sm;
}

.searchInput {
  padding-left: $spacing-10;
}

// Table cell styles
.idCell {
  font-family: $font-mono;
  font-size: $font-size-sm;
  color: $gray-500;
}

.brandCell {
  .badge {
    background-color: $secondary-50;
    color: $secondary-700;
    border: 1px solid $secondary-200;
  }
}

.productTypeCell {
  .badge {
    background-color: $primary-50;
    color: $primary-700;
    border: 1px solid $primary-200;
  }
}

.actionsCell {
  @include flex-center;
  gap: $spacing-2;
  justify-content: flex-start;
}

.deleteButton {
  @include flex-center;
  padding: $spacing-2;
  min-width: auto;

  &:hover {
    background-color: $red-50;
    border-color: $red-200;
    color: $red-600;
  }
}

// Form styles
.form {
  display: flex;
  flex-direction: column;
  gap: $spacing-5;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
}

.label {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $gray-700;
}

.error {
  font-size: $font-size-sm;
  color: $red-600;
  margin-top: $spacing-1;
}

.helpText {
  font-size: $font-size-sm;
  color: $gray-500;
  margin-top: $spacing-1;
}

.formActions {
  @include flex-between;
  gap: $spacing-3;
  margin-top: $spacing-4;
  padding-top: $spacing-4;
  border-top: 1px solid $gray-200;

  @include mobile-only {
    flex-direction: column-reverse;
  }
}

.submitButton {
  @include flex-center;
  gap: $spacing-2;
}

// React Select custom styles
:global(.react-select__control) {
  border: 1px solid $gray-300;
  border-radius: $border-radius-md;
  min-height: 42px;
}

:global(.react-select__control:hover) {
  border-color: $gray-400;
}

:global(.react-select__control--is-focused) {
  border-color: $primary-500;
  box-shadow: 0 0 0 1px $primary-500;
}

:global(.react-select__value-container) {
  padding: $spacing-2 $spacing-3;
}

:global(.react-select__placeholder) {
  color: $gray-500;
}

:global(.react-select__single-value) {
  color: $gray-900;
}

:global(.react-select__multi-value) {
  background-color: $primary-50;
  border-radius: $border-radius-sm;
}

:global(.react-select__multi-value__label) {
  color: $primary-700;
  font-size: $font-size-sm;
}

:global(.react-select__multi-value__remove) {
  color: $primary-500;
}

:global(.react-select__multi-value__remove:hover) {
  background-color: $primary-100;
  color: $primary-700;
}

:global(.react-select__option) {
  padding: $spacing-2 $spacing-3;
}

:global(.react-select__option--is-focused) {
  background-color: $primary-50;
  color: $primary-900;
}

:global(.react-select__option--is-selected) {
  background-color: $primary-500;
  color: $white;
}

:global(.react-select__menu) {
  border: 1px solid $gray-200;
  border-radius: $border-radius-md;
  box-shadow: $shadow-lg;
}

:global(.react-select__menu-list) {
  padding: $spacing-1;
}