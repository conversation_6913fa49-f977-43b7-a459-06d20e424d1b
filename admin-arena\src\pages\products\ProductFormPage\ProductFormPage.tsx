// Product form page for creating products with variants
// Dedicated to POST requests only using create_with_variants endpoint

import React, { useState } from 'react'
import { useNavigate } from '@tanstack/react-router'
import { useForm, Controller, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FiSave, FiArrowLeft, FiPlus, FiTrash2 } from 'react-icons/fi'
import Select from 'react-select'
import {
  useCreateProductWithVariants,
  useProductCategories,
  useProductBrands,
  useProductTypes,
  useAttributeValues
} from '../../../hooks/use-products'
import { Card, CardHeader, CardBody } from '../../../components/ui/Card'
import { Button } from '../../../components/ui/Button'
import { Input } from '../../../components/ui/Input'
import { Switch } from '../../../components/ui/Switch'
import { ButtonLoading } from '../../../components/ui/LoadingSpinner'
import { ImageUploadModal } from '../../../components/products/ImageUploadModal'
import type { ProductCreateWithVariantsResponse } from '../../../types/api-types'
import styles from './ProductFormPage.module.scss'

const productWithVariantsSchema = z.object({
  // Product fields
  title: z.string().min(1, 'Product title is required'),
  slug: z.string().min(1, 'Product slug is required'),
  description: z.string().min(1, 'Product description is required'),
  category: z.number().min(1, 'Category is required'),
  brand: z.number().min(1, 'Brand is required'),
  product_type: z.number().min(1, 'Product type is required'),
  is_active: z.boolean(),
  is_digital: z.boolean(),

  // Variant fields
  variants: z.array(z.object({
    price: z.string().min(1, 'Price is required'),
    price_label: z.number().optional(),
    sku: z.string().min(1, 'SKU is required'),
    stock_qty: z.number().min(0, 'Stock quantity must be 0 or greater'),
    is_active: z.boolean(),
    weight: z.number().optional(),
    condition: z.string().optional(),
  })).min(1, 'At least one variant is required'),
})

type ProductWithVariantsFormData = z.infer<typeof productWithVariantsSchema>

export const ProductFormPage: React.FC = () => {
  const navigate = useNavigate()
  const [createdProduct, setCreatedProduct] = useState<ProductCreateWithVariantsResponse | null>(null)
  const [showImageModal, setShowImageModal] = useState(false)

  const { data: categories } = useProductCategories()
  const { data: brands } = useProductBrands()
  const { data: productTypes } = useProductTypes()
  const { data: attributeValues } = useAttributeValues()

  const createWithVariantsMutation = useCreateProductWithVariants()

  const form = useForm<ProductWithVariantsFormData>({
    resolver: zodResolver(productWithVariantsSchema),
    defaultValues: {
      title: '',
      slug: '',
      description: '',
      category: 0,
      brand: 0,
      product_type: 0,
      is_active: true,
      is_digital: false,
      variants: [{
        price: '',
        sku: '',
        stock_qty: 0,
        is_active: true,
        weight: undefined,
        condition: 'New',
      }],
    },
  })

  const { fields: variantFields, append: appendVariant, remove: removeVariant } = useFieldArray({
    control: form.control,
    name: 'variants',
  })

  // Auto-generate slug from title
  const handleTitleChange = (title: string) => {
    const slug = title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')
    form.setValue('slug', slug)
  }

  // Add new variant
  const addVariant = () => {
    appendVariant({
      price: '',
      sku: '',
      stock_qty: 0,
      is_active: true,
      weight: undefined,
      condition: 'New',
    })
  }

  const handleSubmit = async (data: ProductWithVariantsFormData) => {
    try {
      const result = await createWithVariantsMutation.mutateAsync({
        product: {
          title: data.title,
          slug: data.slug,
          brand: data.brand,
          category: data.category,
          product_type: data.product_type,
          description: data.description,
          is_active: data.is_active,
          is_digital: data.is_digital,
        },
        variants: data.variants,
      })
      setCreatedProduct(result)
      setShowImageModal(true)
    } catch (error) {
      console.error('Form submission failed:', error)
    }
  }

  // Handle image modal close - navigate to edit page with images section
  const handleImageModalClose = () => {
    setShowImageModal(false)
    if (createdProduct) {
      navigate({
        to: '/products/$productId/edit',
        params: { productId: createdProduct.product.id.toString() }
      })
      // After navigation, scroll to images section
      setTimeout(() => {
        const element = document.getElementById('section-images')
        if (element) {
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest'
          })
        }
      }, 100)
    } else {
      navigate({ to: '/products' })
    }
  }

  const isLoading = createWithVariantsMutation.isPending

  // Options for react-select
  const categoryOptions = categories?.map(cat => ({
    value: cat.id,
    label: cat.title,
  })) || []

  const brandOptions = brands?.map(brand => ({
    value: brand.id,
    label: brand.title,
  })) || []

  const productTypeOptions = productTypes?.map(type => ({
    value: type.id,
    label: type.title,
  })) || []

  const attributeValueOptions = attributeValues?.map(value => ({
    value: value.id,
    label: `${value.attribute_title} - ${value.attribute_value}`,
  })) || []

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <Button
            variant="ghost"
            onClick={() => navigate({ to: '/products' })}
            className={styles.backButton}
          >
            <FiArrowLeft />
            Back to Products
          </Button>

          <div className={styles.titleSection}>
            <h1 className={styles.title}>Create Product</h1>
            <p className={styles.subtitle}>
              Add a new product to your catalog with variants
            </p>
          </div>
        </div>

        <div className={styles.headerActions}>
          <Button
            variant="outline"
            onClick={() => navigate({ to: '/products' })}
          >
            Cancel
          </Button>

          <Button
            type="submit"
            variant="primary"
            form="product-form"
            disabled={isLoading}
          >
            <ButtonLoading
              isLoading={isLoading}
              loadingText="Creating..."
            >
              <FiSave />
              Create Product
            </ButtonLoading>
          </Button>
        </div>
      </div>

      <form
        id="product-form"
        onSubmit={form.handleSubmit(handleSubmit)}
        className={styles.form}
      >
        <div className={styles.formGrid}>
          <Card className={styles.mainCard}>
            <CardHeader>
              <h2>Basic Information</h2>
            </CardHeader>

            <CardBody>
              <div className={styles.formSection}>
                <div className={styles.formGroup}>
                  <label htmlFor="title">Product Title *</label>
                  <Input
                    id="title"
                    {...form.register('title')}
                    onChange={(e) => {
                      form.setValue('title', e.target.value)
                      handleTitleChange(e.target.value)
                    }}
                    error={form.formState.errors.title?.message}
                    placeholder="Enter product title"
                  />
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="slug">Product Slug *</label>
                  <Input
                    id="slug"
                    {...form.register('slug')}
                    error={form.formState.errors.slug?.message}
                    placeholder="product-slug"
                    helperText="URL-friendly version of the product title"
                  />
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="description">Description *</label>
                  <textarea
                    id="description"
                    {...form.register('description')}
                    className={styles.textarea}
                    rows={4}
                    placeholder="Enter product description"
                  />
                  {form.formState.errors.description && (
                    <span className={styles.error}>
                      {form.formState.errors.description.message}
                    </span>
                  )}
                </div>
              </div>
            </CardBody>
          </Card>

          <Card className={styles.sideCard}>
            <CardHeader>
              <h2>Product Settings</h2>
            </CardHeader>

            <CardBody>
              <div className={styles.formSection}>
                <div className={styles.formGroup}>
                  <label htmlFor="category">Category *</label>
                  <Controller
                    name="category"
                    control={form.control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        options={categoryOptions}
                        placeholder="Select category..."
                        classNamePrefix="react-select"
                        value={categoryOptions.find(opt => opt.value === field.value) || null}
                        onChange={opt => field.onChange(opt ? opt.value : 0)}
                        styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
                      />
                    )}
                  />
                  {form.formState.errors.category && (
                    <span className={styles.error}>
                      {form.formState.errors.category.message}
                    </span>
                  )}
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="brand">Brand *</label>
                  <Controller
                    name="brand"
                    control={form.control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        options={brandOptions}
                        placeholder="Select brand..."
                        classNamePrefix="react-select"
                        value={brandOptions.find(opt => opt.value === field.value) || null}
                        onChange={opt => field.onChange(opt ? opt.value : 0)}
                        styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
                      />
                    )}
                  />
                  {form.formState.errors.brand && (
                    <span className={styles.error}>
                      {form.formState.errors.brand.message}
                    </span>
                  )}
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="product_type">Product Type *</label>
                  <Controller
                    name="product_type"
                    control={form.control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        options={productTypeOptions}
                        placeholder="Select product type..."
                        classNamePrefix="react-select"
                        value={productTypeOptions.find(opt => opt.value === field.value) || null}
                        onChange={opt => field.onChange(opt ? opt.value : 0)}
                        styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
                      />
                    )}
                  />
                  {form.formState.errors.product_type && (
                    <span className={styles.error}>
                      {form.formState.errors.product_type.message}
                    </span>
                  )}
                </div>

                <div className={styles.formGroup}>
                  <div className={styles.switchGroup}>
                    <label>Is Active</label>
                    <Controller
                      name="is_active"
                      control={form.control}
                      render={({ field }) => (
                        <Switch
                          checked={field.value}
                          onChange={(e) => field.onChange(e.target.checked)}
                        />
                      )}
                    />
                  </div>
                </div>

                <div className={styles.formGroup}>
                  <div className={styles.switchGroup}>
                    <label>Is Digital</label>
                    <Controller
                      name="is_digital"
                      control={form.control}
                      render={({ field }) => (
                        <Switch
                          checked={field.value}
                          onChange={(e) => field.onChange(e.target.checked)}
                        />
                      )}
                    />
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Variants Section */}
        {(
          <Card className={styles.variantsCard}>
            <CardHeader>
              <div className={styles.variantsHeader}>
                <h2>Product Variants</h2>
                <Button
                  type="button"
                  variant="secondary"
                  size="sm"
                  onClick={addVariant}
                >
                  <FiPlus />
                  Add Variant
                </Button>
              </div>
            </CardHeader>

            <CardBody>
              <div className={styles.variantsSection}>
                {variantFields.map((field, index) => (
                  <div key={field.id} className={styles.variantCard}>
                    <div className={styles.variantHeader}>
                      <h4>Variant {index + 1}</h4>
                      {variantFields.length > 1 && (
                        <Button
                          type="button"
                          variant="danger"
                          size="sm"
                          onClick={() => removeVariant(index)}
                        >
                          <FiTrash2 />
                        </Button>
                      )}
                    </div>

                    <div className={styles.variantForm}>
                      <div className={styles.formRow}>
                        <div className={styles.formGroup}>
                          <label>SKU *</label>
                          <Input
                            {...form.register(`variants.${index}.sku`)}
                            placeholder="Enter SKU"
                            error={form.formState.errors.variants?.[index]?.sku?.message}
                          />
                        </div>

                        <div className={styles.formGroup}>
                          <label>Price *</label>
                          <Input
                            {...form.register(`variants.${index}.price`)}
                            placeholder="0.00"
                            error={form.formState.errors.variants?.[index]?.price?.message}
                          />
                        </div>

                        <div className={styles.formGroup}>
                          <label>Stock Quantity *</label>
                          <Input
                            type="number"
                            {...form.register(`variants.${index}.stock_qty`, { valueAsNumber: true })}
                            placeholder="0"
                            min="0"
                            error={form.formState.errors.variants?.[index]?.stock_qty?.message}
                          />
                        </div>

                        <div className={styles.formGroup}>
                          <label>Weight (grams)</label>
                          <Input
                            type="number"
                            {...form.register(`variants.${index}.weight`, { valueAsNumber: true })}
                            placeholder="0"
                            min="0"
                          />
                        </div>
                      </div>

                      <div className={styles.formRow}>
                        <div className={styles.formGroup}>
                          <label>Price Label</label>
                          <Controller
                            name={`variants.${index}.price_label`}
                            control={form.control}
                            render={({ field }) => (
                              <Select
                                {...field}
                                options={attributeValueOptions}
                                placeholder="Select price label..."
                                classNamePrefix="react-select"
                                value={attributeValueOptions.find(opt => opt.value === field.value) || null}
                                onChange={opt => field.onChange(opt ? opt.value : undefined)}
                                styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
                                isClearable
                              />
                            )}
                          />
                        </div>

                        <div className={styles.formGroup}>
                          <div className={styles.switchGroup}>
                            <label>Is Active</label>
                            <Controller
                              name={`variants.${index}.is_active`}
                              control={form.control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={(e) => field.onChange(e.target.checked)}
                                />
                              )}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        )}
      </form>

      {/* Image Upload Modal */}
      {createdProduct && (
        <ImageUploadModal
          isOpen={showImageModal}
          onClose={handleImageModalClose}
          variants={createdProduct.variants}
        />
      )}
    </div>
  )
}

export default ProductFormPage
