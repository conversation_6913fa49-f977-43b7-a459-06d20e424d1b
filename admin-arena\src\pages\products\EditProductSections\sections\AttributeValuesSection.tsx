// Attribute Values Section for associating attribute values with product variants
// Handles bulk operations and individual management

import React, { useState } from 'react'
import { FiPlus, FiTrash2, FiSave, FiX, FiTag, FiCheck } from 'react-icons/fi'
import {
  useVariantAttributeValues,
  useCreateVariantAttributeValue,
  useDeleteVariantAttributeValue,
  useBulkAssociateVariantAttributeValues,
  useBulkUpdateVariantAttributeValueStatus,
  useAttributeValues,
  useAttributes,
  useProductTypeAttributes
} from '../../../../hooks/use-products'
import { Card, CardHeader, CardBody } from '../../../../components/ui/Card'
import { Button } from '../../../../components/ui/Button'
import { Switch } from '../../../../components/ui/Switch'
import { Modal } from '../../../../components/ui/Modal'
import { ButtonLoading, PageLoading } from '../../../../components/ui/LoadingSpinner'
import { Badge } from '../../../../components/ui/Badge'
import type { Product, ProductVariant } from '../../../../types/api-types'
import styles from './AttributeValuesSection.module.scss'



interface AttributeValueGroup {
  attribute: {
    id: number
    title: string
  }
  values: Array<{
    id: number
    attribute_value: string
    is_active: boolean
  }>
}

interface AttributeValuesSectionProps {
  product: Product
}

export const AttributeValuesSection: React.FC<AttributeValuesSectionProps> = ({ product }) => {
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null)
  const [isBulkModalOpen, setIsBulkModalOpen] = useState(false)
  const [selectedAssociations, setSelectedAssociations] = useState<number[]>([])
  const [deletingAssociation, setDeletingAssociation] = useState<any>(null)
  const [selectedAttributeValues, setSelectedAttributeValues] = useState<number[]>([])

  const variants = product.variants || []
  const { data: variantAttributeValues, isLoading: attributeValuesLoading } = useVariantAttributeValues(selectedVariant?.id || 0)
  const { data: allAttributeValues } = useAttributeValues()
  const { data: attributes } = useAttributes()
  const { data: productTypeAttributes } = useProductTypeAttributes(product.product_type?.id || 0)

  const createAssociationMutation = useCreateVariantAttributeValue()
  const deleteAssociationMutation = useDeleteVariantAttributeValue()
  const bulkAssociateMutation = useBulkAssociateVariantAttributeValues()
  const bulkUpdateStatusMutation = useBulkUpdateVariantAttributeValueStatus()



  // Set first variant as selected by default
  React.useEffect(() => {
    if (variants && variants.length > 0 && !selectedVariant) {
      setSelectedVariant(variants[0])
    }
  }, [variants, selectedVariant])

  const handleBulkAssociate = async () => {
    if (!selectedVariant || selectedAttributeValues.length === 0) return

    try {
      await bulkAssociateMutation.mutateAsync({
        product_variant_id: selectedVariant.id,
        attribute_value_ids: selectedAttributeValues,
      })
      setIsBulkModalOpen(false)
      setSelectedAttributeValues([])
    } catch (error) {
      console.error('Failed to bulk associate:', error)
    }
  }

  const handleDeleteAssociation = async () => {
    if (!deletingAssociation) return

    try {
      await deleteAssociationMutation.mutateAsync(deletingAssociation.id)
      setDeletingAssociation(null)
    } catch (error) {
      console.error('Failed to delete association:', error)
    }
  }

  const handleBulkStatusUpdate = async (isActive: boolean) => {
    if (selectedAssociations.length === 0) return

    try {
      await bulkUpdateStatusMutation.mutateAsync({
        association_ids: selectedAssociations,
        is_active: isActive,
      })
      setSelectedAssociations([])
    } catch (error) {
      console.error('Failed to update status:', error)
    }
  }

  const toggleAssociationSelection = (associationId: number) => {
    setSelectedAssociations(prev =>
      prev.includes(associationId)
        ? prev.filter(id => id !== associationId)
        : [...prev, associationId]
    )
  }

  const selectAllAssociations = () => {
    if (!variantAttributeValues) return
    setSelectedAssociations(variantAttributeValues.map(assoc => assoc.id))
  }

  const clearSelection = () => {
    setSelectedAssociations([])
  }

  // Get relevant attribute IDs from product type
  const relevantAttributeIds = productTypeAttributes?.map(pta => pta.attribute.id) || []

  // Filter attribute values to only include those from relevant attributes
  const relevantAttributeValues = allAttributeValues?.filter(value =>
    relevantAttributeIds.includes(value.attribute)
  ) || []

  // Get already associated attribute value IDs for the selected variant
  const associatedValueIds = variantAttributeValues?.map(assoc => assoc.attribute_value.id) || []

  // Group relevant attribute values by attribute, excluding already associated ones
  const attributeValueGroups: AttributeValueGroup[] = relevantAttributeIds.map(attributeId => {
    const attribute = attributes?.find(attr => attr.id === attributeId)
    if (!attribute) return null

    const values = relevantAttributeValues
      .filter(value => value.attribute === attributeId && !associatedValueIds.includes(value.id))
      .map(value => ({
        id: value.id,
        attribute_value: value.attribute_value,
        is_active: value.is_active
      }))

    return {
      attribute: {
        id: attribute.id,
        title: attribute.title
      },
      values
    }
  }).filter((group): group is AttributeValueGroup => group !== null && group.values.length > 0)

  const toggleAttributeValueSelection = (valueId: number) => {
    setSelectedAttributeValues(prev =>
      prev.includes(valueId)
        ? prev.filter(id => id !== valueId)
        : [...prev, valueId]
    )
  }

  if (variants.length === 0) {
    return (
      <Card>
        <CardHeader>
          <h2>Attribute Values</h2>
          <p>Associate attribute values with product variants</p>
        </CardHeader>
        <CardBody>
          <div className={styles.emptyState}>
            <FiTag size={48} />
            <h3>No Variants Available</h3>
            <p>You need to create product variants before you can associate attribute values.</p>
          </div>
        </CardBody>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className={styles.header}>
            <div>
              <h2>Attribute Values</h2>
              <p>Associate attribute values with product variants</p>
            </div>
            <Button
              variant="primary"
              size="sm"
              onClick={() => setIsBulkModalOpen(true)}
              disabled={!selectedVariant}
            >
              <FiPlus />
              Add Attributes
            </Button>
          </div>
        </CardHeader>
        <CardBody>
          {/* Variant Selector */}
          <div className={styles.variantSelector}>
            <label>Select Variant:</label>
            <div className={styles.variantTabs}>
              {variants.map((variant) => (
                <button
                  key={variant.id}
                  className={`${styles.variantTab} ${selectedVariant?.id === variant.id ? styles.active : ''
                    }`}
                  onClick={() => setSelectedVariant(variant)}
                >
                  <span className={styles.variantSku}>{variant.sku}</span>
                  <Badge variant={variant.is_active ? 'success' : 'error'} size="sm">
                    {variant.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                </button>
              ))}
            </div>
          </div>

          {/* Attribute Values Management */}
          {selectedVariant && (
            <div className={styles.attributesSection}>
              <div className={styles.sectionHeader}>
                <h3>Attributes for {selectedVariant.sku}</h3>
                <div className={styles.bulkActions}>
                  {selectedAssociations.length > 0 && (
                    <>
                      <span className={styles.selectionCount}>
                        {selectedAssociations.length} selected
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleBulkStatusUpdate(true)}
                        disabled={bulkUpdateStatusMutation.isPending}
                      >
                        <FiCheck />
                        Activate
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleBulkStatusUpdate(false)}
                        disabled={bulkUpdateStatusMutation.isPending}
                      >
                        <FiX />
                        Deactivate
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={clearSelection}
                      >
                        Clear
                      </Button>
                    </>
                  )}
                  {variantAttributeValues && variantAttributeValues.length > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={selectAllAssociations}
                    >
                      Select All
                    </Button>
                  )}
                </div>
              </div>

              {attributeValuesLoading ? (
                <div className={styles.loadingState}>
                  <PageLoading message="Loading attribute values..." />
                </div>
              ) : !variantAttributeValues || variantAttributeValues.length === 0 ? (
                <div className={styles.emptyAttributes}>
                  <FiTag size={48} />
                  <h4>No Attributes</h4>
                  <p>No attribute values have been associated with this variant yet.</p>
                  <Button
                    variant="outline"
                    onClick={() => setIsBulkModalOpen(true)}
                  >
                    <FiPlus />
                    Add First Attribute
                  </Button>
                </div>
              ) : (
                <div className={styles.attributesList}>
                  {variantAttributeValues.map((association) => (
                    <div key={association.id} className={styles.attributeCard}>
                      <div className={styles.attributeInfo}>
                        <input
                          type="checkbox"
                          checked={selectedAssociations.includes(association.id)}
                          onChange={() => toggleAssociationSelection(association.id)}
                          className={styles.checkbox}
                        />
                        <div className={styles.attributeDetails}>
                          <div className={styles.attributeHeader}>
                            <h4>{association.attribute_value.attribute.title}</h4>
                            <Badge variant={association.is_active ? 'success' : 'error'}>
                              {association.is_active ? 'Active' : 'Inactive'}
                            </Badge>
                          </div>
                          <p className={styles.attributeValue}>
                            {association.attribute_value.attribute_value}
                          </p>
                        </div>
                      </div>
                      <div className={styles.attributeActions}>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setDeletingAssociation(association)}
                          className={styles.deleteButton}
                        >
                          <FiTrash2 />
                          Remove
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </CardBody>
      </Card>

      {/* Bulk Associate Modal */}
      <Modal
        isOpen={isBulkModalOpen}
        onClose={() => {
          setIsBulkModalOpen(false)
          setSelectedAttributeValues([])
        }}
        title="Associate Attribute Values"
        size="xl"
      >
        <div className={styles.modalForm}>
          {/* Modal Header with Product and Variant Info */}
          <div className={styles.modalHeader}>
            <h3 className={styles.productTitle}>{product.title}</h3>
            <p className={styles.variantInfo}>
              {selectedVariant?.price_label || selectedVariant?.sku}
            </p>
          </div>

          {/* Attribute Value Groups */}
          <div className={styles.attributeGroups}>
            {attributeValueGroups.length === 0 ? (
              <div className={styles.noOptions}>
                <p>All available attribute values are already associated with this variant.</p>
              </div>
            ) : (
              attributeValueGroups.map((group) => (
                <div key={group.attribute.id} className={styles.attributeGroup}>
                  <h4 className={styles.attributeGroupTitle}>{group.attribute.title}</h4>
                  <div className={styles.attributeValuesList}>
                    {group.values.map((value) => (
                      <label key={value.id} className={styles.attributeValueItem}>
                        <input
                          type="checkbox"
                          checked={selectedAttributeValues.includes(value.id)}
                          onChange={() => toggleAttributeValueSelection(value.id)}
                          className={styles.attributeValueCheckbox}
                        />
                        <span className={styles.attributeValueLabel}>
                          {value.attribute_value}
                        </span>
                        {!value.is_active && (
                          <Badge variant="error" size="sm">Inactive</Badge>
                        )}
                      </label>
                    ))}
                  </div>
                </div>
              ))
            )}
          </div>

          <div className={styles.modalActions}>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setIsBulkModalOpen(false)
                setSelectedAttributeValues([])
              }}
              disabled={bulkAssociateMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="primary"
              onClick={handleBulkAssociate}
              disabled={bulkAssociateMutation.isPending || selectedAttributeValues.length === 0}
            >
              <ButtonLoading
                isLoading={bulkAssociateMutation.isPending}
                loadingText="Adding..."
              >
                <FiPlus />
                Add {selectedAttributeValues.length} Attribute{selectedAttributeValues.length !== 1 ? 's' : ''}
              </ButtonLoading>
            </Button>
          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={!!deletingAssociation}
        onClose={() => setDeletingAssociation(null)}
        title="Remove Attribute"
        size="sm"
      >
        <div className={styles.deleteModal}>
          {deletingAssociation && (
            <>
              <p>
                Are you sure you want to remove the attribute{' '}
                <strong>
                  {deletingAssociation.attribute_value.attribute.title}: {deletingAssociation.attribute_value.attribute_value}
                </strong>{' '}
                from this variant? This action cannot be undone.
              </p>
              <div className={styles.modalActions}>
                <Button
                  variant="outline"
                  onClick={() => setDeletingAssociation(null)}
                  disabled={deleteAssociationMutation.isPending}
                >
                  Cancel
                </Button>
                <Button
                  variant="danger"
                  onClick={handleDeleteAssociation}
                  disabled={deleteAssociationMutation.isPending}
                >
                  <ButtonLoading
                    isLoading={deleteAssociationMutation.isPending}
                    loadingText="Removing..."
                  >
                    <FiTrash2 />
                    Remove
                  </ButtonLoading>
                </Button>
              </div>
            </>
          )}
        </div>
      </Modal>
    </>
  )
}

export default AttributeValuesSection
