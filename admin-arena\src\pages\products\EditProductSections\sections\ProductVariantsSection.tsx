// Product Variants Section for managing product variants
// Handles CRUD operations for variants with individual Save/Cancel buttons

import React, { useState } from 'react'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FiPlus, FiEdit, FiTrash2, FiSave, FiX } from 'react-icons/fi'
import Select from 'react-select'
import {
  useProductVariants,
  useCreateVariant,
  useUpdateProductVariant,
  useDeleteVariant,
  useAttributeValues
} from '../../../../hooks/use-products'
import { Card, CardHeader, CardBody } from '../../../../components/ui/Card'
import { Button } from '../../../../components/ui/Button'
import { Input } from '../../../../components/ui/Input'
import { Switch } from '../../../../components/ui/Switch'
import { ButtonLoading, PageLoading } from '../../../../components/ui/LoadingSpinner'
import { Modal } from '../../../../components/ui/Modal'
import { Badge } from '../../../../components/ui/Badge'
import type { Product, ProductVariant } from '../../../../types/api-types'
import styles from './ProductVariantsSection.module.scss'

const variantSchema = z.object({
  price: z.string().min(1, 'Price is required'),
  price_label: z.number().optional(),
  sku: z.string().min(1, 'SKU is required'),
  stock_qty: z.number().min(0, 'Stock quantity must be 0 or greater'),
  is_active: z.boolean(),
  weight: z.number().optional(),
  condition: z.string().optional(),
})

type VariantFormData = z.infer<typeof variantSchema>

interface ProductVariantsSectionProps {
  product: Product
}

export const ProductVariantsSection: React.FC<ProductVariantsSectionProps> = ({ product }) => {
  const [editingVariant, setEditingVariant] = useState<ProductVariant | null>(null)
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [deletingVariant, setDeletingVariant] = useState<ProductVariant | null>(null)

  const variants = product.variants || []
  const { data: attributeValues } = useAttributeValues()
  const createVariantMutation = useCreateVariant()
  const updateVariantMutation = useUpdateProductVariant()
  const deleteVariantMutation = useDeleteVariant()

  const createForm = useForm<VariantFormData>({
    resolver: zodResolver(variantSchema),
    defaultValues: {
      price: '',
      sku: '',
      stock_qty: 0,
      is_active: true,
      weight: undefined,
      condition: 'New',
    },
  })

  const editForm = useForm<VariantFormData>({
    resolver: zodResolver(variantSchema),
  })

  // Options for react-select
  const attributeValueOptions = attributeValues?.map(value => ({
    value: value.id,
    label: `${value.attribute_title} - ${value.attribute_value}`,
  })) || []

  const conditionOptions = [
    { value: 'New', label: 'New' },
    { value: 'Used', label: 'Used' },
    { value: 'Refurbished', label: 'Refurbished' },
  ]

  const handleCreateVariant = async (data: VariantFormData) => {
    try {
      await createVariantMutation.mutateAsync({
        ...data,
        product: product.id,
      })
      setIsCreateModalOpen(false)
      createForm.reset()
    } catch (error) {
      console.error('Failed to create variant:', error)
    }
  }

  const handleEditVariant = (variant: ProductVariant) => {
    setEditingVariant(variant)
    editForm.reset({
      price: variant.price.toString(),
      price_label: variant.price_label || undefined,
      sku: variant.sku,
      stock_qty: variant.stock_qty,
      is_active: variant.is_active,
      weight: variant.weight || undefined,
      condition: variant.condition || 'New',
    })
  }

  const handleUpdateVariant = async (data: VariantFormData) => {
    if (!editingVariant) return

    try {
      await updateVariantMutation.mutateAsync({
        id: editingVariant.id,
        data,
      })
      setEditingVariant(null)
    } catch (error) {
      console.error('Failed to update variant:', error)
    }
  }

  const handleDeleteVariant = async () => {
    if (!deletingVariant) return

    try {
      await deleteVariantMutation.mutateAsync(deletingVariant.id)
      setDeletingVariant(null)
    } catch (error) {
      console.error('Failed to delete variant:', error)
    }
  }

  const handleCancelEdit = () => {
    setEditingVariant(null)
    editForm.reset()
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className={styles.header}>
            <div>
              <h2>Product Variants</h2>
              <p>Manage product variants, pricing, SKUs, and inventory</p>
            </div>
            <Button
              variant="primary"
              size="sm"
              onClick={() => setIsCreateModalOpen(true)}
            >
              <FiPlus />
              Add Variant
            </Button>
          </div>
        </CardHeader>
        <CardBody>
          {variants.length === 0 ? (
            <div className={styles.emptyState}>
              <p>No variants found for this product.</p>
              <Button
                variant="outline"
                onClick={() => setIsCreateModalOpen(true)}
              >
                <FiPlus />
                Create First Variant
              </Button>
            </div>
          ) : (
            <div className={styles.variantsList}>
              {variants.map((variant) => (
                <div key={variant.id} className={styles.variantCard}>
                  {editingVariant?.id === variant.id ? (
                    <form
                      onSubmit={editForm.handleSubmit(handleUpdateVariant)}
                      className={styles.editForm}
                    >
                      <div className={styles.formGrid}>
                        <div className={styles.formGroup}>
                          <label>SKU *</label>
                          <Input
                            {...editForm.register('sku')}
                            error={editForm.formState.errors.sku?.message}
                            placeholder="Enter SKU"
                          />
                        </div>
                        <div className={styles.formGroup}>
                          <label>Price *</label>
                          <Input
                            {...editForm.register('price')}
                            error={editForm.formState.errors.price?.message}
                            placeholder="0.00"
                          />
                        </div>
                        <div className={styles.formGroup}>
                          <label>Stock Quantity *</label>
                          <Input
                            type="number"
                            {...editForm.register('stock_qty', { valueAsNumber: true })}
                            error={editForm.formState.errors.stock_qty?.message}
                            placeholder="0"
                            min="0"
                          />
                        </div>
                        <div className={styles.formGroup}>
                          <label>Weight (grams)</label>
                          <Input
                            type="number"
                            {...editForm.register('weight', { valueAsNumber: true })}
                            placeholder="0"
                            min="0"
                          />
                        </div>
                      </div>

                      <div className={styles.formGrid}>
                        <div className={styles.formGroup}>
                          <label>Price Label</label>
                          <Controller
                            name="price_label"
                            control={editForm.control}
                            render={({ field }) => (
                              <Select
                                {...field}
                                options={attributeValueOptions}
                                placeholder="Select price label..."
                                classNamePrefix="react-select"
                                value={attributeValueOptions.find(opt => opt.value === field.value) || null}
                                onChange={opt => field.onChange(opt ? opt.value : undefined)}
                                styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
                                isClearable
                              />
                            )}
                          />
                        </div>
                        <div className={styles.formGroup}>
                          <label>Condition</label>
                          <Controller
                            name="condition"
                            control={editForm.control}
                            render={({ field }) => (
                              <Select
                                {...field}
                                options={conditionOptions}
                                placeholder="Select condition..."
                                classNamePrefix="react-select"
                                value={conditionOptions.find(opt => opt.value === field.value) || null}
                                onChange={opt => field.onChange(opt ? opt.value : 'New')}
                                styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
                              />
                            )}
                          />
                        </div>
                        <div className={styles.formGroup}>
                          <div className={styles.switchGroup}>
                            <label>Is Active</label>
                            <Controller
                              name="is_active"
                              control={editForm.control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={(e) => field.onChange(e.target.checked)}
                                />
                              )}
                            />
                          </div>
                        </div>
                      </div>

                      <div className={styles.formActions}>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={handleCancelEdit}
                          disabled={updateVariantMutation.isPending}
                        >
                          <FiX />
                          Cancel
                        </Button>
                        <Button
                          type="submit"
                          variant="primary"
                          size="sm"
                          disabled={updateVariantMutation.isPending}
                        >
                          <ButtonLoading
                            isLoading={updateVariantMutation.isPending}
                            loadingText="Saving..."
                          >
                            <FiSave />
                            Save
                          </ButtonLoading>
                        </Button>
                      </div>
                    </form>
                  ) : (
                    <div className={styles.variantDisplay}>
                      <div className={styles.variantInfo}>
                        <div className={styles.variantHeader}>
                          <h4>{variant.sku}</h4>
                          <div className={styles.variantBadges}>
                            <Badge variant={variant.is_active ? 'success' : 'error'}>
                              {variant.is_active ? 'Active' : 'Inactive'}
                            </Badge>
                            {variant.condition && (
                              <Badge variant="info">{variant.condition}</Badge>
                            )}
                          </div>
                        </div>
                        <div className={styles.variantDetails}>
                          <div className={styles.detailItem}>
                            <span className={styles.label}>Price:</span>
                            <span className={styles.value}>${variant.price}</span>
                          </div>
                          <div className={styles.detailItem}>
                            <span className={styles.label}>Stock:</span>
                            <span className={styles.value}>{variant.stock_qty}</span>
                          </div>
                          {variant.weight && (
                            <div className={styles.detailItem}>
                              <span className={styles.label}>Weight:</span>
                              <span className={styles.value}>{variant.weight}g</span>
                            </div>
                          )}
                          {variant.price_label_title && (
                            <div className={styles.detailItem}>
                              <span className={styles.label}>Price Label:</span>
                              <span className={styles.value}>{variant.price_label_title}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      <div className={styles.variantActions}>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditVariant(variant)}
                        >
                          <FiEdit />
                          Edit
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setDeletingVariant(variant)}
                          className={styles.deleteButton}
                        >
                          <FiTrash2 />
                          Delete
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardBody>
      </Card>

      {/* Create Variant Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Create New Variant"
        size="lg"
      >
        <form onSubmit={createForm.handleSubmit(handleCreateVariant)} className={styles.modalForm}>
          <div className={styles.formGrid}>
            <div className={styles.formGroup}>
              <label>SKU *</label>
              <Input
                {...createForm.register('sku')}
                error={createForm.formState.errors.sku?.message}
                placeholder="Enter SKU"
              />
            </div>
            <div className={styles.formGroup}>
              <label>Price *</label>
              <Input
                {...createForm.register('price')}
                error={createForm.formState.errors.price?.message}
                placeholder="0.00"
              />
            </div>
            <div className={styles.formGroup}>
              <label>Stock Quantity *</label>
              <Input
                type="number"
                {...createForm.register('stock_qty', { valueAsNumber: true })}
                error={createForm.formState.errors.stock_qty?.message}
                placeholder="0"
                min="0"
              />
            </div>
            <div className={styles.formGroup}>
              <label>Weight (grams)</label>
              <Input
                type="number"
                {...createForm.register('weight', { valueAsNumber: true })}
                placeholder="0"
                min="0"
              />
            </div>
          </div>

          <div className={styles.formGrid}>
            <div className={styles.formGroup}>
              <label>Price Label</label>
              <Controller
                name="price_label"
                control={createForm.control}
                render={({ field }) => (
                  <Select
                    {...field}
                    options={attributeValueOptions}
                    placeholder="Select price label..."
                    classNamePrefix="react-select"
                    value={attributeValueOptions.find(opt => opt.value === field.value) || null}
                    onChange={opt => field.onChange(opt ? opt.value : undefined)}
                    styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
                    isClearable
                  />
                )}
              />
            </div>
            <div className={styles.formGroup}>
              <label>Condition</label>
              <Controller
                name="condition"
                control={createForm.control}
                render={({ field }) => (
                  <Select
                    {...field}
                    options={conditionOptions}
                    placeholder="Select condition..."
                    classNamePrefix="react-select"
                    value={conditionOptions.find(opt => opt.value === field.value) || null}
                    onChange={opt => field.onChange(opt ? opt.value : 'New')}
                    styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
                  />
                )}
              />
            </div>
            <div className={styles.formGroup}>
              <div className={styles.switchGroup}>
                <label>Is Active</label>
                <Controller
                  name="is_active"
                  control={createForm.control}
                  render={({ field }) => (
                    <Switch
                      checked={field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                    />
                  )}
                />
              </div>
            </div>
          </div>

          <div className={styles.modalActions}>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsCreateModalOpen(false)}
              disabled={createVariantMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={createVariantMutation.isPending}
            >
              <ButtonLoading
                isLoading={createVariantMutation.isPending}
                loadingText="Creating..."
              >
                <FiPlus />
                Create Variant
              </ButtonLoading>
            </Button>
          </div>
        </form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={!!deletingVariant}
        onClose={() => setDeletingVariant(null)}
        title="Delete Variant"
        size="sm"
      >
        <div className={styles.deleteModal}>
          <p>
            Are you sure you want to delete the variant <strong>{deletingVariant?.sku}</strong>?
            This action cannot be undone.
          </p>
          <div className={styles.modalActions}>
            <Button
              variant="outline"
              onClick={() => setDeletingVariant(null)}
              disabled={deleteVariantMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              variant="danger"
              onClick={handleDeleteVariant}
              disabled={deleteVariantMutation.isPending}
            >
              <ButtonLoading
                isLoading={deleteVariantMutation.isPending}
                loadingText="Deleting..."
              >
                <FiTrash2 />
                Delete
              </ButtonLoading>
            </Button>
          </div>
        </div>
      </Modal>
    </>
  )
}

export default ProductVariantsSection
