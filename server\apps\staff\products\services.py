import uuid
from django.db import transaction
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.db.models import Q, Avg
from django.db import models
from .models import (
    ProductProxy, CategoryProxy, BrandProxy, ProductTypeProxy, AttributeProxy, AttributeValueProxy,
    ProductVariantProxy, ProductImageProxy, ReviewProxy, DiscountProxy, ProductTypeAttributeProxy,
    ProductAttributeValueProxy, ProductVariantAttributeValueProxy, BrandProductTypeProxy,
    ProductAudit, BulkProductOperation
)
import logging

logger = logging.getLogger(__name__)


class ProductService:
    """
    Service class for product-related operations
    Handles business logic and complex operations
    """

    @staticmethod
    def create_product_with_variants(product_data, variants_data, staff_user, request=None):
        """
        Create a product with variants in a single transaction
        """
        try:
            with transaction.atomic():
                # Create the product
                product = ProductProxy.objects.create(**product_data)

                # Create variants
                created_variants = []
                for variant_data in variants_data:
                    variant_data['product'] = product
                    variant = ProductVariantProxy.objects.create(**variant_data)
                    created_variants.append(variant)

                # Log the action
                # AuditService.log_product_action(
                #     product=product,
                #     staff_user=staff_user,
                #     action='CREATE',
                #     changes={
                #         'product_data': product_data,
                #         'variants_count': len(created_variants)
                #     },
                #     request=request
                # )

                return product, created_variants

        except Exception as e:
            logger.error(f"Error creating product with variants: {str(e)}")
            raise ValidationError(f"Failed to create product: {str(e)}")

    @staticmethod
    def bulk_update_products(products_data, staff_user, request=None):
        """
        Handle bulk product updates with the audit trail
        """
        operation_id = uuid.uuid4()

        # Create bulk operation record
        bulk_op = BulkProductOperation.objects.create(
            operation_id=operation_id,
            staff_user=staff_user,
            operation_type='BULK_UPDATE',
            total_items=len(products_data),
            operation_data={'products': products_data}
        )

        try:
            bulk_op.status = 'IN_PROGRESS'
            bulk_op.save()

            updated_products = []
            failed_items = []

            with transaction.atomic():
                for item_data in products_data:
                    try:
                        product_id = item_data.pop('id')
                        product = ProductProxy.objects.get(id=product_id)

                        # Store original values for audit
                        original_values = {
                            field: getattr(product, field)
                            for field in item_data.keys()
                        }

                        # Update product
                        for field, value in item_data.items():
                            setattr(product, field, value)
                        product.save()

                        # Log individual update
                        AuditService.log_product_action(
                            product=product,
                            staff_user=staff_user,
                            action='BULK_UPDATE',
                            changes={
                                'original': original_values,
                                'updated': item_data,
                                'bulk_operation_id': str(operation_id)
                            },
                            request=request
                        )

                        updated_products.append(product)
                        bulk_op.processed_items += 1

                    except Exception as e:
                        failed_items.append({
                            'item': item_data,
                            'error': str(e)
                        })
                        bulk_op.failed_items += 1
                        logger.error(f"Failed to update product in bulk: {str(e)}")

            # Update operation status
            bulk_op.results = {
                'updated_count': len(updated_products),
                'failed_items': failed_items
            }

            if bulk_op.failed_items == 0:
                bulk_op.mark_completed()
            else:
                bulk_op.status = 'COMPLETED'
                bulk_op.completed_at = timezone.now()
                bulk_op.save()

            return {
                'operation_id': operation_id,
                'updated_products': updated_products,
                'failed_items': failed_items,
                'total_processed': bulk_op.processed_items,
                'total_failed': bulk_op.failed_items
            }

        except Exception as e:
            bulk_op.mark_failed(str(e))
            logger.error(f"Bulk update operation failed: {str(e)}")
            raise ValidationError(f"Bulk update failed: {str(e)}")

    @staticmethod
    def associate_attributes_bulk(product_type_id, attribute_data, staff_user, request=None):
        """
        Bulk associate attributes to a product type
        """
        try:
            with transaction.atomic():
                product_type = ProductTypeProxy.objects.get(id=product_type_id)
                created_associations = []

                for attr_data in attribute_data:
                    attribute_id = attr_data['attribute_id']
                    is_filterable = attr_data.get('is_filterable', False)
                    is_option_selector = attr_data.get('is_option_selector', False)

                    association, created = ProductTypeAttributeProxy.objects.get_or_create(
                        product_type=product_type,
                        attribute_id=attribute_id,
                        defaults={
                            'is_filterable': is_filterable,
                            'is_option_selector': is_option_selector
                        }
                    )

                    if not created:
                        # Update existing association
                        association.is_filterable = is_filterable
                        association.is_option_selector = is_option_selector
                        association.save()

                    created_associations.append(association)

                # Log the action
                AuditService.log_action(
                    action='BULK_ATTRIBUTE_ASSOCIATION',
                    staff_user=staff_user,
                    details={
                        'product_type_id': product_type_id,
                        'associations_count': len(created_associations),
                        'attribute_data': attribute_data
                    },
                    request=request
                )

                return created_associations

        except Exception as e:
            logger.error(f"Error in bulk attribute association: {str(e)}")
            raise ValidationError(f"Failed to associate attributes: {str(e)}")

    @staticmethod
    def get_product_analytics(product_id=None, date_range=None):
        """
        Get product analytics data
        """
        queryset = ProductProxy.objects.all()

        if product_id:
            queryset = queryset.filter(id=product_id)

        analytics = {
            'total_products': queryset.count(),
            'active_products': queryset.filter(is_active=True).count(),
            'products_with_variants': queryset.filter(product_variant__isnull=False).distinct().count(),
            'products_with_reviews': queryset.filter(reviews__isnull=False).distinct().count(),
            'average_rating': queryset.aggregate(avg_rating=models.Avg('average_rating'))['avg_rating'] or 0,
        }

        return analytics


class CategoryService:
    """
    Service class for category-related operations
    """

    @staticmethod
    def move_category(category_id, new_parent_id, staff_user, request=None):
        """
        Move category to a new parent in the tree
        """
        try:
            with transaction.atomic():
                category = CategoryProxy.objects.get(id=category_id)
                old_parent = category.parent

                if new_parent_id:
                    new_parent = CategoryProxy.objects.get(id=new_parent_id)
                    category.parent = new_parent
                else:
                    category.parent = None

                category.save()

                # Log the action
                AuditService.log_action(
                    action='CATEGORY_MOVE',
                    staff_user=staff_user,
                    details={
                        'category_id': category_id,
                        'old_parent_id': old_parent.id if old_parent else None,
                        'new_parent_id': new_parent_id
                    },
                    request=request
                )

                return category

        except Exception as e:
            logger.error(f"Error moving category: {str(e)}")
            raise ValidationError(f"Failed to move category: {str(e)}")


class AuditService:
    """
    Service class for audit trail operations
    """

    @staticmethod
    def log_product_action(product, staff_user, action, changes=None, request=None):
        """
        Log product-related actions
        """
        audit_data = {
            'product': product,
            'staff_user': staff_user,
            'action': action,
            'changes': changes or {},
        }

        if request:
            audit_data.update({
                'ip_address': SecurityService.get_client_ip(request),
                'user_agent': SecurityService.get_user_agent(request)
            })

        return ProductAudit.objects.create(**audit_data)

    @staticmethod
    def log_action(action, staff_user, details=None, request=None):
        """
        Log general actions (non-product specific)
        """
        # This could be extended to a general audit log model
        logger.info(f"Action: {action} by {staff_user.email} - Details: {details}")


class SecurityService:
    """
    Service class for security-related operations
    """

    @staticmethod
    def get_client_ip(request):
        """
        Get the client IP address from the request
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
        return ip

    @staticmethod
    def get_user_agent(request):
        """
        Get the user agent from the request
        """
        return request.META.get('HTTP_USER_AGENT', '')


class ValidationService:
    """
    Service class for data validation
    """

    @staticmethod
    def validate_product_data(data):
        """
        Validate product data before creation/update
        """
        errors = {}

        # Check required fields
        required_fields = ['title', 'brand', 'product_type', 'category']
        for field in required_fields:
            if field not in data or not data[field]:
                errors[field] = f"{field} is required"

        # Validate slug uniqueness if provided
        if 'slug' in data:
            existing = ProductProxy.objects.filter(slug=data['slug'])
            if 'id' in data:
                existing = existing.exclude(id=data['id'])
            if existing.exists():
                errors['slug'] = "Product with this slug already exists"

        if errors:
            raise ValidationError(errors)

        return True


class BrandProductTypeService:
    """
    Service class for brand-product type association operations
    """

    @staticmethod
    def associate_product_types_bulk(brand_id, product_type_ids, staff_user, request=None):
        """
        Bulk associate product types with a brand
        """
        try:
            with transaction.atomic():
                brand = BrandProxy.objects.get(id=brand_id)
                created_associations = []
                existing_associations = []

                for product_type_id in product_type_ids:
                    association, created = BrandProductTypeProxy.objects.get_or_create(
                        brand=brand,
                        product_type_id=product_type_id
                    )

                    if created:
                        created_associations.append(association)
                    else:
                        existing_associations.append(association)

                # Log the action
                AuditService.log_action(
                    action='BULK_BRAND_PRODUCT_TYPE_ASSOCIATION',
                    staff_user=staff_user,
                    details={
                        'brand_id': brand_id,
                        'product_type_ids': product_type_ids,
                        'created_count': len(created_associations),
                        'existing_count': len(existing_associations)
                    },
                    request=request
                )

                return {
                    'created_associations': created_associations,
                    'existing_associations': existing_associations,
                    'total_created': len(created_associations),
                    'total_existing': len(existing_associations)
                }

        except Exception as e:
            logger.error(f"Error in bulk brand-product type association: {str(e)}")
            raise ValidationError(f"Failed to associate product types: {str(e)}")

    @staticmethod
    def remove_product_types_bulk(brand_id, product_type_ids, staff_user, request=None):
        """
        Bulk remove product type associations from a brand
        """
        try:
            with transaction.atomic():
                removed_count = BrandProductTypeProxy.objects.filter(
                    brand_id=brand_id,
                    product_type_id__in=product_type_ids
                ).delete()[0]

                # Log the action
                AuditService.log_action(
                    action='BULK_BRAND_PRODUCT_TYPE_REMOVAL',
                    staff_user=staff_user,
                    details={
                        'brand_id': brand_id,
                        'product_type_ids': product_type_ids,
                        'removed_count': removed_count
                    },
                    request=request
                )

                return {'removed_count': removed_count}

        except Exception as e:
            logger.error(f"Error in bulk brand-product type removal: {str(e)}")
            raise ValidationError(f"Failed to remove product type associations: {str(e)}")


class ProductAttributeService:
    """
    Service class for product attribute value operations
    """

    @staticmethod
    def associate_attribute_values_bulk(product_id, attribute_value_ids, staff_user, request=None):
        """
        Bulk associate attribute values with a product
        """
        try:
            with transaction.atomic():
                product = ProductProxy.objects.get(id=product_id)
                created_associations = []
                existing_associations = []

                for attribute_value_id in attribute_value_ids:
                    association, created = ProductAttributeValueProxy.objects.get_or_create(
                        product=product,
                        attribute_value_id=attribute_value_id
                    )

                    if created:
                        created_associations.append(association)
                    else:
                        existing_associations.append(association)

                # Log the action
                AuditService.log_product_action(
                    product=product,
                    staff_user=staff_user,
                    action='PRODUCT_ATTRIBUTE_ASSOCIATION',
                    changes={
                        'attribute_value_ids': attribute_value_ids,
                        'created_count': len(created_associations),
                        'existing_count': len(existing_associations)
                    },
                    request=request
                )

                return {
                    'created_associations': created_associations,
                    'existing_associations': existing_associations,
                    'total_created': len(created_associations),
                    'total_existing': len(existing_associations)
                }

        except Exception as e:
            logger.error(f"Error in bulk product attribute association: {str(e)}")
            raise ValidationError(f"Failed to associate attribute values: {str(e)}")


class VariantAttributeService:
    """
    Service class for variant attribute value operations
    """

    @staticmethod
    def associate_attribute_values_bulk(variant_id, attribute_value_ids, staff_user, request=None):
        """
        Bulk associate attribute values with a product variant
        """
        try:
            with transaction.atomic():
                variant = ProductVariantProxy.objects.get(id=variant_id)
                created_associations = []
                existing_associations = []

                for attribute_value_id in attribute_value_ids:
                    association, created = ProductVariantAttributeValueProxy.objects.get_or_create(
                        product_variant=variant,
                        attribute_value_id=attribute_value_id,
                        defaults={'is_active': True}
                    )

                    if created:
                        created_associations.append(association)
                    else:
                        existing_associations.append(association)

                # Log the action
                AuditService.log_product_action(
                    product=variant.product,
                    staff_user=staff_user,
                    action='VARIANT_ATTRIBUTE_ASSOCIATION',
                    changes={
                        'variant_sku': variant.sku,
                        'attribute_value_ids': attribute_value_ids,
                        'created_count': len(created_associations),
                        'existing_count': len(existing_associations)
                    },
                    request=request
                )

                return {
                    'created_associations': created_associations,
                    'existing_associations': existing_associations,
                    'total_created': len(created_associations),
                    'total_existing': len(existing_associations)
                }

        except Exception as e:
            logger.error(f"Error in bulk variant attribute association: {str(e)}")
            raise ValidationError(f"Failed to associate attribute values: {str(e)}")

    @staticmethod
    def update_association_status_bulk(association_ids, is_active, staff_user, request=None):
        """
        Bulk update active status of variant attribute associations
        """
        try:
            with transaction.atomic():
                associations = ProductVariantAttributeValueProxy.objects.filter(
                    id__in=association_ids
                )

                # Get affected variants for logging
                affected_variants = list(associations.values_list(
                    'product_variant__sku', flat=True
                ).distinct())

                updated_count = associations.update(is_active=is_active)

                # Log the action
                AuditService.log_action(
                    action='VARIANT_ATTRIBUTE_STATUS_UPDATE',
                    staff_user=staff_user,
                    details={
                        'association_ids': association_ids,
                        'is_active': is_active,
                        'updated_count': updated_count,
                        'affected_variants': affected_variants
                    },
                    request=request
                )

                return {'updated_count': updated_count}

        except Exception as e:
            logger.error(f"Error in bulk variant attribute status update: {str(e)}")
            raise ValidationError(f"Failed to update association status: {str(e)}")
