@use '../../../../scss/variables.scss' as *;
@use '../../../../scss/mixins.scss' as *;

.header {
  @include flex-between;
  align-items: flex-start;
  gap: $spacing-md;

  div {
    h2 {
      @include heading-md;
      margin: 0 0 $spacing-xs 0;
      color: $text-primary;
    }

    p {
      @include text-sm;
      margin: 0;
      color: $text-secondary;
    }
  }
}

.emptyState {
  @include flex-center;
  flex-direction: column;
  gap: $spacing-lg;
  padding: $spacing-xl;
  text-align: center;
  color: $text-secondary;

  svg {
    color: $text-placeholder;
  }

  h3 {
    @include heading-sm;
    margin: 0;
    color: $text-primary;
  }

  p {
    @include text-base;
    margin: 0;
  }
}

.variantSelector {
  margin-bottom: $spacing-xl;

  label {
    @include text-sm;
    font-weight: 600;
    color: $text-primary;
    display: block;
    margin-bottom: $spacing-md;
  }
}

.variantTabs {
  display: flex;
  gap: $spacing-sm;
  flex-wrap: wrap;
}

.variantTab {
  @include button-reset;
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-sm $spacing-md;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  background-color: $background-primary;
  transition: all 0.2s ease;

  &:hover {
    border-color: $primary-color;
    background-color: $primary-light;
  }

  &.active {
    border-color: $primary-color;
    background-color: $primary-light;

    .variantSku {
      color: $primary-color;
      font-weight: 600;
    }
  }
}

.variantSku {
  @include text-sm;
  color: $text-primary;
  font-weight: 500;
}

.attributesSection {
  border-top: 1px solid $border-color;
  padding-top: $spacing-lg;
}

.sectionHeader {
  @include flex-between;
  align-items: center;
  margin-bottom: $spacing-lg;

  h3 {
    @include heading-sm;
    margin: 0;
    color: $text-primary;
  }
}

.bulkActions {
  @include flex-start;
  gap: $spacing-sm;
  align-items: center;
}

.selectionCount {
  @include text-sm;
  color: $text-secondary;
  font-weight: 500;
  padding: $spacing-xs $spacing-sm;
  background-color: $background-secondary;
  border-radius: $border-radius-sm;
}

.loadingState {
  @include flex-center;
  padding: $spacing-xl;
}

.emptyAttributes {
  @include flex-center;
  flex-direction: column;
  gap: $spacing-lg;
  padding: $spacing-xl;
  text-align: center;
  color: $text-secondary;

  svg {
    color: $text-placeholder;
  }

  h4 {
    @include heading-sm;
    margin: 0;
    color: $text-primary;
  }

  p {
    @include text-base;
    margin: 0;
  }
}

.attributesList {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
}

.attributeCard {
  @include flex-between;
  align-items: center;
  padding: $spacing-md;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  background-color: $background-primary;
  transition: all 0.2s ease;

  &:hover {
    border-color: $primary-light;
    background-color: $background-hover;
  }
}

.attributeInfo {
  @include flex-start;
  align-items: center;
  gap: $spacing-md;
  flex: 1;
  min-width: 0;
}

.checkbox {
  width: 16px;
  height: 16px;
  accent-color: $primary-color;
  cursor: pointer;
}

.attributeDetails {
  flex: 1;
  min-width: 0;
}

.attributeHeader {
  @include flex-between;
  align-items: center;
  margin-bottom: $spacing-xs;

  h4 {
    @include text-sm;
    font-weight: 600;
    color: $text-primary;
    margin: 0;
  }
}

.attributeValue {
  @include text-sm;
  color: $text-secondary;
  margin: 0;
}

.attributeActions {
  @include flex-start;
  gap: $spacing-sm;
  flex-shrink: 0;
}

.deleteButton {
  color: $error-color;

  &:hover {
    background-color: rgba($error-color, 0.1);
    color: $error-color;
  }
}

.modalForm {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.modalHeader {
  padding-bottom: $spacing-lg;
  border-bottom: 1px solid $border-color;
  margin-bottom: $spacing-lg;

  .productTitle {
    @include heading-md;
    margin: 0 0 $spacing-xs 0;
    color: $text-primary;
    font-weight: 600;
  }

  .variantInfo {
    @include text-sm;
    margin: 0;
    color: $text-secondary;
    font-weight: 500;
  }
}

.attributeGroups {
  display: flex;
  flex-direction: column;
  gap: $spacing-xl;
  max-height: 60vh;
  overflow-y: auto;
  padding-right: $spacing-xs;
}

.attributeGroup {
  .attributeGroupTitle {
    @include text-base;
    font-weight: 600;
    color: $text-primary;
    margin: 0 0 $spacing-md 0;
    padding-bottom: $spacing-sm;
    border-bottom: 1px solid $border-light;
  }
}

.attributeValuesList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: $spacing-sm;
}

.attributeValueItem {
  @include flex-start;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-sm;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  background-color: $background-primary;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: $primary-color;
    background-color: $primary-light;
  }

  &:has(.attributeValueCheckbox:checked) {
    border-color: $primary-color;
    background-color: $primary-light;
  }
}

.attributeValueCheckbox {
  width: 16px;
  height: 16px;
  accent-color: $primary-color;
  cursor: pointer;
  flex-shrink: 0;
}

.attributeValueLabel {
  @include text-sm;
  color: $text-primary;
  flex: 1;
  min-width: 0;
}

.formGroup {
  label {
    @include text-sm;
    font-weight: 500;
    color: $text-primary;
    display: block;
    margin-bottom: $spacing-sm;
  }
}

.error {
  @include text-xs;
  color: $error-color;
  margin-top: $spacing-xs;
}

.noOptions {
  padding: $spacing-lg;
  text-align: center;
  background-color: $background-secondary;
  border-radius: $border-radius;

  p {
    @include text-sm;
    color: $text-secondary;
    margin: 0;
  }
}

.modalActions {
  @include flex-end;
  gap: $spacing-md;
  padding-top: $spacing-lg;
  border-top: 1px solid $border-color;
}

.deleteModal {
  text-align: center;

  p {
    @include text-base;
    color: $text-primary;
    margin-bottom: $spacing-xl;

    strong {
      color: $error-color;
    }
  }
}

// React Select custom styles
:global(.react-select__control) {
  @include input-base;
  border: 1px solid $border-color !important;
  box-shadow: none !important;
  min-height: 40px;

  &:hover {
    border-color: $primary-color !important;
  }
}

:global(.react-select__control--is-focused) {
  border-color: $primary-color !important;
  box-shadow: 0 0 0 2px rgba($primary-color, 0.1) !important;
}

:global(.react-select__value-container) {
  padding: 0 $spacing-sm;
}

:global(.react-select__placeholder) {
  color: $text-placeholder;
}

:global(.react-select__multi-value) {
  background-color: $primary-light;
  border-radius: $border-radius-sm;
}

:global(.react-select__multi-value__label) {
  color: $primary-color;
  font-size: $font-size-xs;
}

:global(.react-select__multi-value__remove) {
  color: $primary-color;

  &:hover {
    background-color: $primary-color;
    color: white;
  }
}

:global(.react-select__menu) {
  border: 1px solid $border-color;
  box-shadow: $shadow-md;
  z-index: 9999;
}

:global(.react-select__group-heading) {
  background-color: $background-secondary;
  color: $text-primary;
  font-weight: 600;
  font-size: $font-size-xs;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: $spacing-sm;
}

:global(.react-select__option) {
  &:hover {
    background-color: $background-hover;
  }
}

:global(.react-select__option--is-selected) {
  background-color: $primary-color;
}

:global(.react-select__option--is-focused) {
  background-color: $primary-light;
}

// Responsive design
@media (max-width: 768px) {
  .variantTabs {
    flex-direction: column;
  }

  .variantTab {
    justify-content: space-between;
  }

  .sectionHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-md;
  }

  .bulkActions {
    flex-wrap: wrap;
  }

  .attributeCard {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-md;
  }

  .attributeActions {
    align-self: flex-end;
  }

  .attributeValuesList {
    grid-template-columns: 1fr;
  }

  .attributeGroups {
    max-height: 50vh;
  }

  .modalHeader {
    .productTitle {
      @include heading-sm;
    }
  }
}