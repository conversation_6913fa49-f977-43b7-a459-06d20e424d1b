// Core API Types for Admin Arena
// Based on Django backend models and staff app APIs

export interface PaginatedResponse<T> {
  count: number
  next: string | null
  previous: string | null
  results: T[]
}

export interface ApiError {
  message: string
  status?: number
  data?: Record<string, string[]>
}

// Authentication Types
export interface StaffUser {
  id: number
  email: string
  is_staff: boolean
  is_superuser: boolean
  is_active: boolean
  staff_profile?: StaffProfile
  groups: string[]
  permissions: string[]
}

export interface StaffProfile {
  id: number
  employee_id: string
  department: 'PRODUCT' | 'ORDER' | 'CUSTOMER' | 'CONTENT' | 'FINANCE' | 'ADMIN' | 'IT'
  position_title: string
  manager?: StaffProfile
  hire_date: string
  status: 'ACTIVE' | 'INACTIVE' | 'ON_LEAVE' | 'TERMINATED'
  full_name: string
  is_manager: boolean
  team_size: number
}

export interface AuthTokens {
  access: string
  refresh: string
}

export interface LoginCredentials {
  username: string
  password: string
}

export interface LoginResponse {
  access: string
  refresh: string
  user: StaffUser
}

export interface CurrentUserResponse {
  user: StaffUser
  permissions: string[]
  groups: string[]
}

// Filter Types
export interface OrderFilters {
  status?: string
  assigned_to?: number
  customer?: number
  date_from?: string
  date_to?: string
  search?: string
  page?: number
  page_size?: number
  ordering?: string
}

export interface ProductFilters {
  category?: number
  brand?: number
  is_active?: boolean
  search?: string
  price_min?: number
  price_max?: number
  page?: number
  page_size?: number
  ordering?: string
}

export interface CustomerFilters {
  is_active?: boolean
  search?: string
  date_joined_from?: string
  date_joined_to?: string
  page?: number
  page_size?: number
  ordering?: string
}

// Order Types
export interface Order {
  id: number
  order_number: string
  customer: {
    id: number
    email: string
    first_name: string
    last_name: string
  }
  status: 'PENDING' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED'
  total_amount: number
  created_at: string
  updated_at: string
  assigned_to?: StaffProfile
  items: OrderItem[]
}

export interface OrderItem {
  id: number
  product_variant: {
    id: number
    product: {
      id: number
      name: string
      slug: string
    }
    sku: string
    price: number
  }
  quantity: number
  unit_price: number
  total_price: number
}

export interface OrderNote {
  id: number
  order: number
  staff_member: StaffProfile
  note: string
  is_internal: boolean
  created_at: string
}

export interface OrderStatusUpdate {
  status: 'PENDING' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED'
  notes?: string
  notify_customer?: boolean
}

// Product Types
export interface Product {
  id: number
  title: string
  slug: string
  description?: string
  category?: number
  category_title?: string
  brand?: number
  brand_title?: string
  product_type?: number
  product_type_title?: string
  is_active: boolean
  is_digital?: boolean
  average_rating: number
  created_at: string
  updated_at: string
  variants?: ProductVariant[]
  variants_count?: number
  total_stock?: number
  reviews_count?: number
  active_variants_count?: number
}

export interface Category {
  id: number
  title: string
  slug?: string
  description?: string
  parent?: number
  level?: number
  is_active?: boolean
  children?: Category[]
  children_count?: number
  products_count?: number
  full_path?: string
}

export interface Brand {
  id: number
  title: string
  slug?: string
  logo?: string
  is_active?: boolean
  products_count?: number
  product_types?: number[]
}

export interface ProductType {
  id: number
  title: string
  parent?: number
  attributes_count?: number
  products_count?: number
}

export interface ProductVariant {
  id: number
  product: number
  sku: string
  price: number
  price_label?: number
  price_label_title?: string
  stock_qty: number
  is_active: boolean
  weight?: number
  condition?: string
  order?: number
  created_at: string
  updated_at: string
  images?: ProductImage[]
  attribute_values?: AttributeValue[]
}

export interface Attribute {
  id: number
  title: string
  values_count?: number
  product_types?: string[]
}

// Brand-Product Type Association interfaces
export interface BrandProductType {
  id: number
  brand: number
  brand_title: string
  product_type: number
  product_type_title: string
}

export interface BrandProductTypeBulkAssociate {
  brand: number[]
  product_type_ids: number[]
}

// Product Type-Attribute Association interfaces
export interface ProductTypeAttribute {
  id: number
  product_type: number
  product_type_title?: string
  attribute: number
  attribute_title?: string
  is_filterable: boolean
  is_option_selector: boolean
}

export interface ProductTypeAttributeAssociation {
  attribute_id: number
  is_filterable: boolean
  is_option_selector: boolean
}

export interface AttributeValue {
  id: number
  attribute_value: string
  attribute: number
  attribute_title?: string
  is_active: boolean
  products_count?: number
}

// Attribute Value form and API interfaces
export interface AttributeValueFormData {
  attribute_value: string
  is_active: boolean
}

export interface AttributeValueCreateData {
  attribute: number
  attribute_value: string
  is_active: boolean
}

export interface AttributeValueBulkCreateData {
  attribute: number
  values: string[]
}

// Product Image interfaces
export interface ProductImage {
  id: number
  image: string
  alternative_text: string
  order: number
  product_variant: number
}

export interface ProductImageCreateData {
  image: File
  alternative_text: string
  product_variant: number
}

// Product creation with variants interfaces
export interface ProductCreateData {
  title: string
  slug: string
  brand: number
  category: number
  product_type: number
  description: string
  is_active: boolean
  is_digital: boolean
}

export interface ProductVariantCreateData {
  price: string
  price_label?: number
  sku: string
  stock_qty: number
  is_active: boolean
  weight?: number
  condition?: string
}

export interface ProductCreateWithVariantsData {
  product: ProductCreateData
  variants: ProductVariantCreateData[]
}

export interface ProductCreateWithVariantsResponse {
  product: Product
  variants: ProductVariant[]
}

export interface ProductImage {
  id: number
  alternative_text?: string
  image: string
  product_variant: number
  order: number
}

// Customer Types
export interface Customer {
  id: number
  email: string
  first_name: string
  last_name: string
  phone_number?: string
  is_active: boolean
  date_joined: string
  last_login?: string
  addresses: Address[]
  orders_count: number
  total_spent: number
}

export interface Address {
  id: number
  customer: number
  full_name: string
  address_line_1: string
  address_line_2?: string
  city_or_village: string
  postal_code: string
  country: string
  is_primary: boolean
}

// Dashboard Types
export interface DashboardStats {
  orders: {
    total: number
    pending: number
    processing: number
    shipped: number
    delivered: number
    cancelled: number
  }
  products: {
    total: number
    active: number
    inactive: number
    low_stock: number
  }
  customers: {
    total: number
    active: number
    new_this_month: number
  }
  revenue: {
    today: number
    this_week: number
    this_month: number
    this_year: number
  }
}

// Notification Types
export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  actions?: Array<{
    label: string
    action: () => void
  }>
}

// Bulk Operation Types
export interface BulkOperation {
  id: string
  type: 'orders' | 'products' | 'customers'
  action: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
  total: number
  completed: number
  failed: number
  started_at: string
  completed_at?: string
  error?: string
}

// Staff Groups (from Django backend)
export const STAFF_GROUPS = {
  SUPER_ADMIN: 'Super Administrator (SA)',
  STAFF_MANAGER: 'Staff Manager (SM)',
  DEPARTMENT_HEAD: 'Department Head (DH)',
  HR_ADMINISTRATOR: 'HR Administrator (HRA)',
  PRODUCT_MANAGER: 'Product Management Executive (PME)',
  PRODUCT_TEAM_MEMBER: 'Product Management Group Member (PMGM)',
  PRODUCT_VIEWER: 'Product Catalog Viewer (PCV)',
  INVENTORY_MANAGER: 'Inventory Management Executive (IME)',
  ORDER_MANAGER: 'Order Management Executive (OME)',
  ORDER_TEAM_MEMBER: 'Order Management Group Member (OMGM)',
  ORDER_FULFILLMENT: 'Order Fulfillment Specialist (OFS)',
  CUSTOMER_MANAGER: 'Customer Management Executive (CME)',
  CUSTOMER_SERVICE: 'Customer Service Representative (CSR)',
  CUSTOMER_ANALYST: 'Customer Data Analyst (CDA)',
  CONTENT_MANAGER: 'Content Management Executive (CME)',
  CONTENT_MODERATOR: 'Content Moderator (CM)',
  FINANCE_MANAGER: 'Finance Manager (FM)',
  BUSINESS_ANALYST: 'Business Intelligence Analyst (BIA)',
} as const

export type StaffGroupKey = keyof typeof STAFF_GROUPS
export type StaffGroupValue = typeof STAFF_GROUPS[StaffGroupKey]

// Filter Types
export interface OrderFilters {
  page?: number
  page_size?: number
  search?: string
  status?: string
  assigned_to?: number
  date_from?: string
  date_to?: string
  ordering?: string
}

export interface ProductFilters {
  page?: number
  page_size?: number
  search?: string
  category?: number
  brand?: number
  product_type?: number
  is_active?: boolean
  ordering?: string
}

export interface CustomerFilters {
  page?: number
  page_size?: number
  search?: string
  is_active?: boolean
  date_from?: string
  date_to?: string
  ordering?: string
}
